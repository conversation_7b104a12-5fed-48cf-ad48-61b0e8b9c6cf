<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    @php
        $companyName = isset($settings)
            ? $settings['company_name'] ?? 'PT Digital Decision Indonesia'
            : 'PT Digital Decision Indonesia';
        $companyTagline = isset($settings)
            ? $settings['company_tagline'] ?? 'Leading ERP Solutions & Business Automation'
            : 'Leading ERP Solutions & Business Automation';
        $companyDescription = isset($settings)
            ? $settings['company_description'] ??
                'Premier software house untuk solusi ERP custom dan business automation. Official Odoo partner pertama di Riau.'
            : 'Premier software house untuk solusi ERP custom dan business automation. Official Odoo partner pertama di Riau.';
    @endphp

    <title>@yield('title', $companyName . ' - ' . $companyTagline)</title>
    <meta name="description" content="@yield('description', $companyDescription)">
    <meta name="keywords" content="@yield('keywords', 'ERP Riau, Odoo Partner, Software House Pekanbaru, Custom ERP Indonesia')">
    <meta name="author" content="{{ $companyName }}">
    <meta name="robots" content="index, follow">

    <!-- Open Graph -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="@yield('title', $companyName . ' - Official Odoo Partner Riau')">
    <meta property="og:description" content="@yield('description', $companyDescription)">
    <meta property="og:image" content="@yield('og_image', asset('logo.png'))">
    <meta property="og:site_name" content="{{ $companyName }}">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('title', $companyName . ' - Official Odoo Partner Riau')">
    <meta name="twitter:description" content="@yield('description', $companyDescription)">
    <meta name="twitter:image" content="@yield('og_image', asset('logo.png'))">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('favicon.png') }}">
    <link rel="apple-touch-icon" href="{{ asset('logo.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "{{ $companyName }}",
        "description": "{{ $companyDescription }}",
        "url": "{{ url('/') }}",
        "logo": "{{ asset('logo.png') }}",
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "{{ isset($settings) ? ($settings['contact_phone'] ?? '+6282171469407') : '+6282171469407' }}",
            "contactType": "customer service",
            "areaServed": "ID",
            "availableLanguage": "Indonesian"
        },
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "{{ isset($settings) ? ($settings['contact_address'] ?? 'Jalan Rawa Mulya No 3A, Sidomulyo Timur') : 'Jalan Rawa Mulya No 3A, Sidomulyo Timur' }}",
            "addressLocality": "Pekanbaru",
            "addressRegion": "Riau",
            "addressCountry": "ID"
        }
    }
    </script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'primary': {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .service-card {
            transition: all 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .stats-counter {
            font-weight: 700;
            font-size: 2.5rem;
        }

        @media (max-width: 768px) {
            .stats-counter {
                font-size: 2rem;
            }
        }
    </style>

    @stack('styles')
</head>

<body class="font-sans antialiased">
    <!-- Professional Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-100 fixed w-full z-50 transition-all duration-300"
        id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo Section -->
                <div class="flex items-center">
                    <a href="{{ route('landing.index') }}" class="flex items-center space-x-3 group">
                        <img class="h-8 w-auto" src="{{ asset('logo.png') }}" alt="{{ $companyName }}">
                        <div class="hidden sm:block">
                            <div class="text-lg font-semibold text-gray-900">
                                {{ $companyName }}
                            </div>
                            <div class="text-xs text-primary-600 font-medium -mt-1">
                                Official Odoo Partner Riau
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    @if (isset($navigation) && $navigation->count() > 0)
                        @foreach ($navigation as $item)
                            <a href="{{ $item->href }}"
                                class="text-gray-700 hover:text-primary-600 text-sm font-medium transition-colors duration-200"
                                @if ($item->target === '_blank') target="_blank" @endif>
                                {{ $item->label }}
                            </a>
                        @endforeach
                    @else
                        <!-- Fallback navigation -->
                        <a href="#home" class="text-gray-700 hover:text-primary-600 text-sm font-medium">Home</a>
                        <a href="#services"
                            class="text-gray-700 hover:text-primary-600 text-sm font-medium">Services</a>
                        <a href="#about" class="text-gray-700 hover:text-primary-600 text-sm font-medium">About</a>
                        <a href="#portfolio"
                            class="text-gray-700 hover:text-primary-600 text-sm font-medium">Portfolio</a>
                        <a href="#contact" class="text-gray-700 hover:text-primary-600 text-sm font-medium">Contact</a>
                    @endif
                </div>

                <!-- CTA Section -->
                <div class="hidden md:flex items-center space-x-4">
                    @php
                        $contactPhone = isset($settings)
                            ? $settings['contact_phone'] ?? '+6282171469407'
                            : '+6282171469407';
                    @endphp
                    <a href="tel:{{ $contactPhone }}"
                        class="text-gray-600 hover:text-primary-600 text-sm font-medium transition-colors duration-200">
                        {{ $contactPhone }}
                    </a>
                    <a href="#contact"
                        class="bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors duration-200">
                        Konsultasi Gratis
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-button text-gray-700 hover:text-primary-600 p-2">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-4 py-4 space-y-2 bg-white border-t border-gray-100">
                @if (isset($navigation) && $navigation->count() > 0)
                    @foreach ($navigation as $item)
                        <a href="{{ $item->href }}"
                            class="block text-gray-700 hover:text-primary-600 py-2 text-base font-medium transition-colors duration-200"
                            @if ($item->target === '_blank') target="_blank" @endif>
                            {{ $item->label }}
                        </a>
                    @endforeach
                @else
                    <!-- Fallback mobile navigation -->
                    <a href="#home" class="block text-gray-700 hover:text-primary-600 py-2">Home</a>
                    <a href="#services" class="block text-gray-700 hover:text-primary-600 py-2">Services</a>
                    <a href="#about" class="block text-gray-700 hover:text-primary-600 py-2">About</a>
                    <a href="#portfolio" class="block text-gray-700 hover:text-primary-600 py-2">Portfolio</a>
                    <a href="#contact" class="block text-gray-700 hover:text-primary-600 py-2">Contact</a>
                @endif

                <div class="pt-4 mt-4 border-t border-gray-200 space-y-3">
                    <a href="tel:{{ $contactPhone }}"
                        class="block text-gray-600 hover:text-primary-600 py-2 text-sm">
                        {{ $contactPhone }}
                    </a>
                    <a href="#contact"
                        class="block bg-primary-600 text-white px-4 py-3 rounded-lg text-center font-medium hover:bg-primary-700 transition-colors duration-200">
                        Konsultasi Gratis
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <main class="pt-16">
        @yield('content')
    </main>

    <!-- Professional Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <img class="h-8 w-auto mr-3" src="{{ asset('logo.png') }}" alt="{{ $companyName }}">
                        <span class="text-xl font-semibold">{{ $companyName }}</span>
                    </div>
                    <p class="text-gray-300 mb-4">{{ $companyDescription }}</p>
                    <div class="flex space-x-4">
                        @if (isset($settings['social_facebook']) && $settings['social_facebook'] !== '#')
                            <a href="{{ $settings['social_facebook'] }}" class="text-gray-300 hover:text-white"
                                target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                </svg>
                            </a>
                        @endif

                        @if (isset($settings['social_instagram']) && $settings['social_instagram'] !== '#')
                            <a href="{{ $settings['social_instagram'] }}" class="text-gray-300 hover:text-white"
                                target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297z" />
                                </svg>
                            </a>
                        @endif

                        @if (isset($settings['social_linkedin']) && $settings['social_linkedin'] !== '#')
                            <a href="{{ $settings['social_linkedin'] }}" class="text-gray-300 hover:text-white"
                                target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                                </svg>
                            </a>
                        @endif

                        @if (isset($settings['social_twitter']) && $settings['social_twitter'] !== '#')
                            <a href="{{ $settings['social_twitter'] }}" class="text-gray-300 hover:text-white"
                                target="_blank">
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                                </svg>
                            </a>
                        @endif
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Kontak</h3>
                    <div class="space-y-2">
                        @php
                            $contactPhone = isset($settings)
                                ? $settings['contact_phone'] ?? '+62 821-7146-9407'
                                : '+62 821-7146-9407';
                            $contactEmail = isset($settings)
                                ? $settings['contact_email'] ?? '<EMAIL>'
                                : '<EMAIL>';
                            $contactAddress = isset($settings)
                                ? $settings['contact_address'] ?? 'Jalan Rawa Mulya No 3A, Sidomulyo Timur, Pekanbaru'
                                : 'Jalan Rawa Mulya No 3A, Sidomulyo Timur, Pekanbaru';
                        @endphp
                        <p class="text-gray-300">{{ $contactPhone }}</p>
                        <p class="text-gray-300">{{ $contactEmail }}</p>
                        <p class="text-gray-300">{{ $contactAddress }}</p>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Layanan</h3>
                    <div class="space-y-2">
                        <a href="#services" class="text-gray-300 hover:text-white block">ERP Implementation</a>
                        <a href="#services" class="text-gray-300 hover:text-white block">Custom Development</a>
                        <a href="#services" class="text-gray-300 hover:text-white block">Business Automation</a>
                        <a href="#services" class="text-gray-300 hover:text-white block">Consulting</a>
                    </div>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-700 text-center">
                <p class="text-gray-300">&copy; {{ date('Y') }} {{ $companyName }}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Enhanced Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');

                    // Animate hamburger icon
                    const svg = mobileMenuButton.querySelector('svg');
                    if (mobileMenu.classList.contains('hidden')) {
                        svg.innerHTML =
                            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />';
                    } else {
                        svg.innerHTML =
                            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />';
                    }
                });
            }

            // Navbar scroll effect
            const navbar = document.getElementById('navbar');
            if (navbar) {
                let lastScrollY = window.scrollY;

                window.addEventListener('scroll', function() {
                    const currentScrollY = window.scrollY;

                    if (currentScrollY > 100) {
                        navbar.classList.add('bg-white/98', 'shadow-xl');
                        navbar.classList.remove('bg-white/95');
                    } else {
                        navbar.classList.add('bg-white/95');
                        navbar.classList.remove('bg-white/98', 'shadow-xl');
                    }

                    // Hide/show navbar on scroll
                    if (currentScrollY > lastScrollY && currentScrollY > 200) {
                        navbar.style.transform = 'translateY(-100%)';
                    } else {
                        navbar.style.transform = 'translateY(0)';
                    }

                    lastScrollY = currentScrollY;
                });
            }

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const offsetTop = target.offsetTop - 100; // Account for fixed navbar
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });

                        // Close mobile menu if open
                        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                            mobileMenu.classList.add('hidden');
                            const svg = mobileMenuButton.querySelector('svg');
                            if (svg) {
                                svg.innerHTML =
                                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />';
                            }
                        }
                    }
                });
            });
        });
    </script>

    @stack('scripts')
</body>

</html>
