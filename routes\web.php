<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\QuotationDocumentController;
use App\Http\Controllers\WhatsAppWebhookController;
use App\Http\Controllers\LandingPageController;

// Landing Page Routes - try with explicit middleware
Route::middleware(['web'])->group(function () {
    Route::get('/', [LandingPageController::class, 'index'])->name('landing.index');

    // SEO Routes
    Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index']);
    Route::get('/robots.txt', [App\Http\Controllers\SitemapController::class, 'robots']);
    Route::get('/page/{slug}', [LandingPageController::class, 'page'])->name('landing.page');

    // Test routes
    Route::get('/test-simple', function () {
        return 'Simple test works!';
    });

    Route::get('/test-layout', function () {
        return view('landing.layout-test');
    });

    Route::get('/test-data', function () {
        $homepage = \App\Models\CmsPage::homepage()->first();
        return response()->json([
            'homepage_exists' => $homepage ? true : false,
            'homepage_title' => $homepage ? $homepage->title : null,
            'total_pages' => \App\Models\CmsPage::count(),
            'total_sections' => \App\Models\CmsSection::count(),
        ]);
    });
});

// CMS Preview Route (for authenticated users)
Route::middleware(['auth'])->group(function () {
    Route::get('/cms/preview/{page}', [LandingPageController::class, 'preview'])->name('cms.preview');
});

// Test route for CRM data
Route::get('/test-crm', function () {
    $stages = \App\Models\CrmPipelineStage::all();
    $leads = \App\Models\CrmLead::with(['contact', 'pipelineStage'])->get();

    return response()->json([
        'stages' => $stages->count(),
        'leads' => $leads->count(),
        'stages_data' => $stages->map(fn($s) => ['id' => $s->id, 'name' => $s->name]),
        'leads_data' => $leads->map(fn($l) => ['id' => $l->id, 'title' => $l->title, 'stage' => $l->pipelineStage->name]),
    ]);
});

// Demo route for enhanced comments
Route::get('/demo/enhanced-comments', function () {
    return view('demo.enhanced-comments');
})->name('demo.enhanced-comments');

// PDF Generation Routes
Route::middleware(['auth'])->prefix('pdf')->group(function () {
    // Download PDF
    Route::get('/quotation/{quotation}', [PdfController::class, 'quotation'])->name('pdf.quotation');
    Route::get('/sales-invoice/{invoice}', [PdfController::class, 'salesInvoice'])->name('pdf.sales-invoice');
    Route::get('/purchase-order/{purchaseOrder}', [PdfController::class, 'purchaseOrder'])->name('pdf.purchase-order');

    // Preview PDF in browser
    Route::get('/preview/quotation/{quotation}', [PdfController::class, 'previewQuotation'])->name('pdf.preview.quotation');
    Route::get('/preview/sales-invoice/{invoice}', [PdfController::class, 'previewSalesInvoice'])->name('pdf.preview.sales-invoice');
});

// Quotation Document Routes
Route::middleware(['auth'])->prefix('quotation')->group(function () {
    Route::get('/{quotation}/document-viewer', [QuotationDocumentController::class, 'documentViewer'])->name('quotation.document-viewer');
    Route::get('/{quotation}/download', [QuotationDocumentController::class, 'download'])->name('quotation.download');
    Route::post('/{quotationId}/save-annotations', [QuotationDocumentController::class, 'saveAnnotations'])->name('quotation.save-annotations');
});

// Quotation Document Routes
Route::middleware(['auth'])->prefix('quotation')->group(function () {
    Route::get('/{quotation}/preview', [QuotationDocumentController::class, 'preview'])->name('quotation.preview');
    Route::get('/{quotation}/editor', [QuotationDocumentController::class, 'editor'])->name('quotation.editor');
    Route::post('/{quotation}/update-document', [QuotationDocumentController::class, 'updateDocument'])->name('quotation.update-document');
    Route::get('/{quotation}/download', [QuotationDocumentController::class, 'download'])->name('quotation.download');
    Route::post('/{quotation}/upload-file', [QuotationDocumentController::class, 'uploadFile'])->name('quotation.upload-file');
    Route::delete('/{quotation}/delete-file', [QuotationDocumentController::class, 'deleteFile'])->name('quotation.delete-file');
});

// WhatsApp Webhook Routes
Route::post('/webhook/whatsapp', [WhatsAppWebhookController::class, 'handle'])
    ->name('whatsapp.webhook')
    ->withoutMiddleware(['web', 'auth']);

Route::get('/webhook/whatsapp/stats', [WhatsAppWebhookController::class, 'getWebhookStats'])
    ->name('whatsapp.webhook.stats')
    ->middleware(['auth']);

// Fallback route to catch all unmatched routes
Route::fallback(function () {
    return response()->json([
        'message' => 'Route not found',
        'url' => request()->url(),
        'method' => request()->method(),
        'all_routes' => collect(\Route::getRoutes())->map(function ($route) {
            return [
                'uri' => $route->uri(),
                'methods' => $route->methods(),
                'name' => $route->getName(),
            ];
        })->take(10)->toArray()
    ], 404);
});
