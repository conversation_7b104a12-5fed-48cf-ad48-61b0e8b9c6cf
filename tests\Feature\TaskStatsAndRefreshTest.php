<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Task;
use App\Models\Project;
use App\Models\User;
use App\Filament\Resources\TaskResource\Widgets\TaskProgressWidget;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TaskStatsAndRefreshTest extends TestCase
{
    use RefreshDatabase;

    public function test_task_stats_widget_calculates_correctly()
    {
        // Create test data
        $user = User::factory()->create();
        $project = Project::create([
            'name' => 'Test Project',
            'description' => 'Test Description',
            'start_date' => now(),
            'status' => 'active',
            'created_by' => $user->id,
        ]);

        // Create tasks with different statuses
        Task::create([
            'project_id' => $project->id,
            'name' => 'Todo Task 1',
            'status' => 'todo',
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        Task::create([
            'project_id' => $project->id,
            'name' => 'Todo Task 2',
            'status' => 'todo',
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        Task::create([
            'project_id' => $project->id,
            'name' => 'In Progress Task',
            'status' => 'in_progress',
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        Task::create([
            'project_id' => $project->id,
            'name' => 'Completed Task',
            'status' => 'completed',
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        // Create overdue task
        Task::create([
            'project_id' => $project->id,
            'name' => 'Overdue Task',
            'status' => 'todo',
            'due_date' => now()->subDays(1),
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        // Test counts
        $this->assertEquals(5, Task::count());
        $this->assertEquals(3, Task::where('status', 'todo')->count());
        $this->assertEquals(1, Task::where('status', 'in_progress')->count());
        $this->assertEquals(1, Task::where('status', 'completed')->count());
        $this->assertEquals(1, Task::where('due_date', '<', now())->whereNotIn('status', ['completed'])->count());
    }

    public function test_task_progress_widget_data_structure()
    {
        // Create test data
        $user = User::factory()->create();
        $project = Project::create([
            'name' => 'Test Project',
            'description' => 'Test Description',
            'start_date' => now(),
            'status' => 'active',
            'created_by' => $user->id,
        ]);

        // Create tasks
        Task::create([
            'project_id' => $project->id,
            'name' => 'Todo Task',
            'status' => 'todo',
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        Task::create([
            'project_id' => $project->id,
            'name' => 'In Progress Task',
            'status' => 'in_progress',
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        Task::create([
            'project_id' => $project->id,
            'name' => 'Completed Task',
            'status' => 'completed',
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        // Test widget data structure using reflection
        $widget = new TaskProgressWidget();
        $reflection = new \ReflectionClass($widget);
        $method = $reflection->getMethod('getData');
        $method->setAccessible(true);
        $data = $method->invoke($widget);

        $this->assertArrayHasKey('datasets', $data);
        $this->assertArrayHasKey('labels', $data);
        $this->assertEquals(['To Do', 'In Progress', 'Completed'], $data['labels']);
        $this->assertCount(1, $data['datasets']);
        $this->assertArrayHasKey('data', $data['datasets'][0]);
        $this->assertArrayHasKey('backgroundColor', $data['datasets'][0]);
    }

    public function test_task_resource_has_widgets()
    {
        $widgets = \App\Filament\Resources\TaskResource::getWidgets();

        $this->assertContains(
            \App\Filament\Resources\TaskResource\Widgets\TaskStatsWidget::class,
            $widgets
        );

        $this->assertContains(
            \App\Filament\Resources\TaskResource\Widgets\MyTasksWidget::class,
            $widgets
        );

        $this->assertContains(
            \App\Filament\Resources\TaskResource\Widgets\TaskProgressWidget::class,
            $widgets
        );
    }

    public function test_task_resource_has_afterStateUpdated_callbacks()
    {
        // This test verifies that the TaskResource table configuration includes afterStateUpdated callbacks
        // We can't easily test the actual callback execution without a full Filament environment
        // But we can verify the resource structure

        $reflection = new \ReflectionClass(\App\Filament\Resources\TaskResource::class);
        $method = $reflection->getMethod('table');

        $this->assertTrue($method->isStatic());
        $this->assertTrue($method->isPublic());
    }

    public function test_task_completion_rate_calculation()
    {
        // Create test data
        $user = User::factory()->create();
        $project = Project::create([
            'name' => 'Test Project',
            'description' => 'Test Description',
            'start_date' => now(),
            'status' => 'active',
            'created_by' => $user->id,
        ]);

        // Create 10 tasks: 3 todo, 2 in_progress, 5 completed
        for ($i = 1; $i <= 3; $i++) {
            Task::create([
                'project_id' => $project->id,
                'name' => "Todo Task {$i}",
                'status' => 'todo',
                'assigned_to' => $user->id,
                'created_by' => $user->id,
            ]);
        }

        for ($i = 1; $i <= 2; $i++) {
            Task::create([
                'project_id' => $project->id,
                'name' => "In Progress Task {$i}",
                'status' => 'in_progress',
                'assigned_to' => $user->id,
                'created_by' => $user->id,
            ]);
        }

        for ($i = 1; $i <= 5; $i++) {
            Task::create([
                'project_id' => $project->id,
                'name' => "Completed Task {$i}",
                'status' => 'completed',
                'assigned_to' => $user->id,
                'created_by' => $user->id,
            ]);
        }

        $totalTasks = Task::count();
        $completedTasks = Task::where('status', 'completed')->count();
        $completionRate = round(($completedTasks / $totalTasks) * 100, 1);

        $this->assertEquals(10, $totalTasks);
        $this->assertEquals(5, $completedTasks);
        $this->assertEquals(50.0, $completionRate);
    }

    public function test_overdue_tasks_calculation()
    {
        // Create test data
        $user = User::factory()->create();
        $project = Project::create([
            'name' => 'Test Project',
            'description' => 'Test Description',
            'start_date' => now(),
            'status' => 'active',
            'created_by' => $user->id,
        ]);

        // Create overdue task (not completed)
        Task::create([
            'project_id' => $project->id,
            'name' => 'Overdue Task',
            'status' => 'todo',
            'due_date' => now()->subDays(2),
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        // Create overdue but completed task (should not count)
        Task::create([
            'project_id' => $project->id,
            'name' => 'Overdue Completed Task',
            'status' => 'completed',
            'due_date' => now()->subDays(1),
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        // Create future task
        Task::create([
            'project_id' => $project->id,
            'name' => 'Future Task',
            'status' => 'todo',
            'due_date' => now()->addDays(1),
            'assigned_to' => $user->id,
            'created_by' => $user->id,
        ]);

        $overdueTasks = Task::where('due_date', '<', now())
            ->whereNotIn('status', ['completed'])
            ->count();

        $this->assertEquals(1, $overdueTasks);
    }
}
