<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Task;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TaskStatsWidgetFilterTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Project $project;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->project = Project::factory()->create();

        Auth::login($this->user);
    }

    public function test_task_filter_urls_are_correctly_formatted()
    {
        // Test that the expected URL format works
        $expectedTotalTasksUrl = route('filament.admin.resources.tasks.index', [
            'tableFilters[assigned_to][value]' => $this->user->id
        ]);

        $expectedActiveTasksUrl = route('filament.admin.resources.tasks.index', [
            'tableFilters[assigned_to][value]' => $this->user->id,
            'tableFilters[status][values][0]' => 'todo',
            'tableFilters[status][values][1]' => 'in_progress'
        ]);

        $expectedOverdueUrl = route('filament.admin.resources.tasks.index', [
            'tableFilters[assigned_to][value]' => $this->user->id,
            'tableFilters[overdue][isActive]' => true
        ]);

        // Verify URLs are properly formatted (URL encoded)
        $this->assertStringContainsString('tableFilters%5Bassigned_to%5D%5Bvalue%5D', $expectedTotalTasksUrl);
        $this->assertStringContainsString('tableFilters%5Bstatus%5D%5Bvalues%5D', $expectedActiveTasksUrl);
        $this->assertStringContainsString('tableFilters%5Boverdue%5D%5BisActive%5D', $expectedOverdueUrl);
    }

    public function test_overdue_filter_works_with_task_model()
    {
        // Create overdue task
        $overdueTask = Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'todo',
            'due_date' => Carbon::yesterday()
        ]);

        // Create non-overdue task
        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'todo',
            'due_date' => Carbon::tomorrow()
        ]);

        // Test overdue scope
        $overdueTasks = Task::overdue()->get();

        $this->assertCount(1, $overdueTasks);
        $this->assertEquals($overdueTask->id, $overdueTasks->first()->id);
    }

    public function test_task_counts_calculation()
    {
        // Create various tasks for the user
        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'todo'
        ]);

        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'in_progress'
        ]);

        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'completed'
        ]);

        // Create overdue task
        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'todo',
            'due_date' => Carbon::yesterday()
        ]);

        // Test counts
        $totalTasks = Task::where('assigned_to', $this->user->id)->count();
        $activeTasks = Task::where('assigned_to', $this->user->id)
            ->whereIn('status', ['todo', 'in_progress'])
            ->count();
        $overdueTasks = Task::where('assigned_to', $this->user->id)
            ->overdue()
            ->count();

        $this->assertEquals(4, $totalTasks);
        $this->assertEquals(3, $activeTasks); // todo + in_progress + overdue todo
        $this->assertEquals(1, $overdueTasks);
    }

    public function test_task_stats_widget_filter_urls()
    {
        // Test URL formats for TaskStatsWidget
        $userId = $this->user->id;

        $expectedTodoUrl = route('filament.admin.resources.tasks.index', [
            'tableFilters[status][value]' => 'todo'
        ]);

        $expectedOverdueUrl = route('filament.admin.resources.tasks.index', [
            'tableFilters[overdue][isActive]' => true
        ]);

        $expectedMyTasksUrl = route('filament.admin.resources.tasks.index', [
            'tableFilters[assigned_to][value]' => $userId,
            'tableFilters[status][values][0]' => 'todo',
            'tableFilters[status][values][1]' => 'in_progress'
        ]);

        // Verify URLs are properly formatted (URL encoded)
        $this->assertStringContainsString('tableFilters%5Bstatus%5D%5Bvalue%5D=todo', $expectedTodoUrl);
        $this->assertStringContainsString('tableFilters%5Boverdue%5D%5BisActive%5D', $expectedOverdueUrl);
        $this->assertStringContainsString('tableFilters%5Bassigned_to%5D%5Bvalue%5D', $expectedMyTasksUrl);
        $this->assertStringContainsString('tableFilters%5Bstatus%5D%5Bvalues%5D', $expectedMyTasksUrl);
    }

    public function test_general_task_filtering_scenarios()
    {
        // Create tasks with different scenarios
        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'todo',
            'due_date' => Carbon::tomorrow()
        ]);

        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'in_progress',
            'due_date' => Carbon::yesterday()
        ]);

        Task::factory()->create([
            'assigned_to' => $this->user->id,
            'project_id' => $this->project->id,
            'status' => 'completed',
            'due_date' => Carbon::yesterday() // Even if overdue, completed tasks shouldn't show in overdue
        ]);

        // Test different filter scenarios
        $todoTasks = Task::where('status', 'todo')->count();
        $overdueTasks = Task::overdue()->count();
        $myActiveTasks = Task::where('assigned_to', $this->user->id)
            ->whereIn('status', ['todo', 'in_progress'])
            ->count();

        $this->assertEquals(1, $todoTasks);
        $this->assertEquals(1, $overdueTasks); // Only the in_progress overdue task
        $this->assertEquals(2, $myActiveTasks); // todo + in_progress
    }
}
